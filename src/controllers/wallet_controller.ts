import { Request, Response } from 'express';

import { asyncHandler } from '../helpers/asyncHandler.js';
import Company from '../models/CompanyModel/CompanyModel.js';
import Transaction from '../models/TransactionModel/TransactionModel.js';
import { TRANSACTION_TYPE } from '../types/transactions.js';
import { UserDoc } from '../types/user.js';

export const getWalletBalance = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  const company = await Company.findOne({ _id: user.company });
  if (!company) {
    return res.status(404).json({ status: 'failed', message: 'Company not found' });
  }
  type AggregationResult = { totalEarned?: number; totalWithdrawn?: number };
  const [earnings, withdrawals] = await Promise.all([
    Transaction.aggregate<AggregationResult>([
      { $match: { companyId: company._id, type: TRANSACTION_TYPE.CREDIT_WALLET_TRANSACTION } },
      { $group: { _id: null, totalEarned: { $sum: '$amount' } } }
    ]),

    Transaction.aggregate<AggregationResult>([
      { $match: { companyId: company._id, type: TRANSACTION_TYPE.WALLET_WITHDRAWAL_TRANSACTION } },
      { $group: { _id: null, totalWithdrawn: { $sum: '$amount' } } }
    ])
  ]);

  const totalEarned = earnings[0]?.totalEarned ?? 0;
  const totalWithdrawn = withdrawals[0]?.totalWithdrawn ?? 0;
  const availableBalance = totalEarned - totalWithdrawn;

  return res.status(200).json({
    status: 'success',
    data: { totalEarned, totalWithdrawn, availableBalance }
  });
});
