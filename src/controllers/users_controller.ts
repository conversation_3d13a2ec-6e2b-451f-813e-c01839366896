import { Request, Response } from 'express';
import mongoose from 'mongoose';

import {
  createClientApiValidator,
  createFleetUserApiValidator,
  OtpApiValidator,
  passwordResetApiValidator,
  resetPasswordTokenApiValidator,
  updatePasswordApiValidator,
  updateUserApiValidator,
  userLoginApiValidator
} from '../api_validators/users-api-validators.js';
import { addPointsToAddress } from '../helpers/application_helper.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import {
  getWalletBalance,
  sanitizeReturnedUser,
  sendOTPToUser,
  sendWelcomeEmail,
  validatePhoneNumber
} from '../helpers/user_helper.js';
import Logger from '../libs/logger.js';
import Car from '../models/CarModel/CarModel.js';
import Company from '../models/CompanyModel/CompanyModel.js';
import PaymentInfo from '../models/PaymentInfoModel/PaymentInfoModel.js';
import User, { UserDocumentResult } from '../models/UserModel/UserModel.js';
import { OTPSendType } from '../types/otp.js';
import { PaymentInfoDoc } from '../types/payment_info.js';
import { RegisterCompanyRequestBody, RegisterUserRequestBody, USER_ROLE, UserDoc } from '../types/user.js';
import { hasExpired } from './../helpers/user_helper.js';

export const createUser = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as RegisterUserRequestBody;
  const { email, phoneNumber, deviceId, role = 'client', fcmToken } = body;

  const { error } = createClientApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const { formattedNumber, isValid } = validatePhoneNumber(phoneNumber);
  if (!isValid) {
    return res.status(422).json({ error: `invalid phone number ${phoneNumber} provided` });
  }

  const newUser = new User({
    email,
    phoneNumber: formattedNumber,
    deviceId,
    fcmToken,
    role
  });
  await newUser.save();

  await sendWelcomeEmail(newUser.toJSON());

  return res.status(201).json({
    status: 'true',
    message: 'User registered successfully',
    data: newUser
  });
});

export const getUsers = asyncHandler(async (req: Request, res: Response) => {
  const currentUser = req.user;

  if (!currentUser._id) {
    return res.status(400).json({
      status: 'failed',
      message: 'User ID is required'
    });
  }

  const pipeline = [
    {
      $match: {
        role: { $ne: 'super-admin' },
        _id: { $ne: new mongoose.Types.ObjectId(currentUser._id) }
      }
    },
    {
      $lookup: {
        from: 'companies',
        localField: 'company',
        foreignField: '_id',
        as: 'company',
        pipeline: [
          {
            $lookup: {
              from: 'paymentinfos',
              localField: 'paymentInfo',
              foreignField: '_id',
              as: 'paymentInfo'
            }
          },
          {
            $unwind: {
              path: '$paymentInfo',
              preserveNullAndEmptyArrays: true
            }
          }
        ]
      }
    },
    {
      $unwind: {
        path: '$company',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: 1,
        email: 1,
        phoneNumber: 1,
        firstName: 1,
        lastName: 1,
        middleName: 1,
        role: 1,
        address: 1,
        fcmToken: 1,
        deviceId: 1,
        createdAt: 1,
        updatedAt: 1,
        company: {
          _id: '$company._id',
          name: '$company.name',
          email: '$company.email',
          phoneNumber: '$company.phoneNumber',
          address: '$company.address',
          paymentInfo: '$company.paymentInfo'
        }
      }
    }
  ];

  const result = await User.aggregate(pipeline);

  return res.status(200).json({
    status: 'success',
    data: result
  });
});

export const getUser = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const user = await User.findOne<UserDocumentResult>({ _id: id });
  if (!user) {
    return res.status(404).json({
      status: 'failed',
      message: 'User not found'
    });
  }
  const company = await Company.findOne({ _id: user.company }).populate<PaymentInfoDoc>('paymentInfo');
  const returnedUser = sanitizeReturnedUser(user._doc, company);
  return res.status(200).json({
    status: 'success',
    data: returnedUser
  });
});

export const getUserCars = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({
      status: 'failed',
      message: 'User ID is required'
    });
  }

  const user = await User.findOne({ _id: id });
  if (!user) {
    return res.status(404).json({
      status: 'failed',
      message: 'User not found'
    });
  }

  const pipeline = [
    {
      $match: {
        createdBy: new mongoose.Types.ObjectId(id),
        isActive: true
      }
    },
    {
      $lookup: {
        from: 'carimages',
        localField: '_id',
        foreignField: 'carId',
        as: 'carImages',
        pipeline: [
          { $match: { isActive: true, isVisible: true } },
          { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'brands',
        localField: 'brand',
        foreignField: '_id',
        as: 'brand'
      }
    },
    {
      $unwind: {
        path: '$brand',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'carcategories',
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'ratings',
        localField: 'rating',
        foreignField: '_id',
        as: 'rating'
      }
    },
    {
      $unwind: {
        path: '$rating',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: 1,
        description: 1,
        engineType: 1,
        schedule: 1,
        address: 1,
        year: 1,
        dailyPrice: 1,
        dailyMinPrice: 1,
        model: 1,
        brand: '$brand.name',
        category: {
          _id: '$category._id',
          name: '$category.name',
          pictureUrl: '$category.pictureUrl'
        },
        carImages: 1,
        rating: {
          rating: '$rating.rating',
          comment: '$rating.comment'
        },
        isAvailable: 1,
        createdAt: 1,
        updatedAt: 1
      }
    }
  ];

  const result = await Car.aggregate(pipeline);

  return res.status(200).json({
    status: 'success',
    data: result
  });
});

export const fleetAppLogin = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as RegisterUserRequestBody;
  const { email, phoneNumber, password, deviceId, fcmToken } = body;
  const { error } = userLoginApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  let formattedNumber: string | undefined;
  if (phoneNumber) {
    const validation = validatePhoneNumber(phoneNumber);
    if (!validation.isValid) {
      return res.status(422).json({ error: `Invalid phone number ${phoneNumber} provided` });
    }
    formattedNumber = validation.formattedNumber;
  }

  const user = await User.findOne<UserDocumentResult>({
    $or: [{ email }, { phoneNumber: formattedNumber }],
    isActive: true
  });

  if (!user) {
    return res.status(401).json({
      status: 'failed',
      message: 'Invalid Credentials'
    });
  }
  const company = await Company.findOne({ _id: user.company }).populate<PaymentInfoDoc>('paymentInfo');
  const returnedUser = sanitizeReturnedUser(user._doc, company);
  const isMatch = await user.matchPassword(password);
  if (!isMatch) {
    return res.status(401).json({
      status: 'failed',
      message: 'Invalid Credentials'
    });
  }
  const authToken = user.getSignedJwtToken();
  const walletBalance = await getWalletBalance(user);
  const paymentInfo = await PaymentInfo.findOne({ company: user.company });
  await User.findOneAndUpdate({ _id: user.id }, { authToken, fcmToken, deviceId });
  return res.status(200).json({
    status: 'success',
    data: { ...returnedUser, authToken, walletBalance, paymentInfo }
  });
});

export const updateUser = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as Omit<RegisterUserRequestBody, 'role'>;
  const { id } = req.params;
  const { lastName, firstName, middleName, address, fcmToken, deviceId } = body;
  const { error } = updateUserApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const user = await User.findOneActive({ _id: id });
  if (!user) {
    return res.status(404).json({ error: `user not found with the id ${id} provided` });
  }

  // Explicitly specify which fields can be updated
  const updateFields = {
    lastName,
    firstName,
    middleName,
    fcmToken,
    deviceId
  };

  const updatedUser = await User.findOneAndUpdate<UserDocumentResult>({ _id: id }, updateFields, { new: true });

  if (address) {
    await Company.findOneAndUpdate({ _id: user.company }, { address });
  }

  const company = await Company.findOne({ _id: user.company }).populate<PaymentInfoDoc>('paymentInfo');
  const returnedUser = sanitizeReturnedUser(updatedUser._doc, company);
  return res.status(200).json({
    status: 'success',
    message: 'User Updated Successfully.',
    data: returnedUser
  });
});

export const deleteUser = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const user = await User.findOneActive({ _id: id });
  if (!user) {
    return res.status(404).json({ error: `user not found with the id ${id} provided` });
  }

  const inactiveUser = await User.findOneAndUpdate<UserDocumentResult>(
    { _id: id },
    { is_active: false },
    { new: true }
  );

  const company = await Company.findOne({ _id: user.company }).populate<PaymentInfoDoc>('paymentInfo');
  const returnedUser = sanitizeReturnedUser(inactiveUser._doc, company);
  return res.status(200).json({
    status: 'success',
    message: 'User deactivated Successfully.',
    data: returnedUser
  });
});

export const getLoggedInUser = (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  return res.status(200).json({
    status: 'success',
    data: user
  });
};

export const logOut = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  await User.findOneAndUpdate<UserDocumentResult>({ _id: user._id }, { token: 'undefined' });
  return res.status(200).json({
    status: 'success',
    message: 'logged out'
  });
});

export const createFleetUser = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as RegisterCompanyRequestBody;
  const { email, phoneNumber, firstName, lastName, middleName, password, deviceId, role, fcmToken, address } = body;

  // Validate input data
  const { error } = createFleetUserApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  // Validate and format phone number
  const { formattedNumber, isValid } = validatePhoneNumber(phoneNumber);
  if (!isValid) {
    return res.status(422).json({ error: `invalid phone number ${phoneNumber} provided` });
  }

  // Check for existing user
  const findUser = await User.findOne({ $or: [{ email }, { phoneNumber: formattedNumber }] });
  if (findUser) {
    return res.status(409).json({ error: `Fleet User Already Exists` });
  }

  // Validate role
  const validRoles = ['fleet', 'manager', 'admin'];
  if (!validRoles.includes(role)) {
    return res.status(422).json({ error: `Invalid role. Must be one of: ${validRoles.join(', ')}` });
  }

  // Format address
  const updateAddress = addPointsToAddress(address);

  // Start a session for transaction
  const session = await User.startSession();
  session.startTransaction();

  try {
    // Create new user
    const newUser = new User({
      email,
      phoneNumber: formattedNumber,
      firstName,
      lastName,
      middleName,
      password,
      deviceId,
      role,
      fcmToken,
      address: updateAddress
    });

    await newUser.save({ session });

    // Create new company
    const newCompany = new Company({
      email,
      phoneNumber: formattedNumber,
      name: `${firstName} ${lastName}`,
      address: updateAddress,
      user: newUser._id
    });
    await newCompany.save({ session });

    // Update user with company reference
    await User.findByIdAndUpdate(newUser._id, { company: newCompany._id }, { session });

    // Create wallet for the company
    try {
      const { WalletService } = await import('../services/wallet_service.js');
      await WalletService.createWallet({
        company: newCompany._id,
        createdBy: newUser._id
      });
    } catch (walletError) {
      Logger.warn('Failed to create wallet for company:', walletError);
      // Don't fail the user creation if wallet creation fails
    }

    // Commit transaction
    await session.commitTransaction();

    // Get populated user data
    const user = await User.findOne<UserDocumentResult>({ _id: newUser._id }).lean();
    const company = await Company.findOne({ _id: newCompany._id }).populate<PaymentInfoDoc>('paymentInfo');
    const returnedUser = sanitizeReturnedUser(user, company);

    // Send welcome email
    const emailSent = await sendWelcomeEmail(user);
    if (!emailSent) {
      Logger.warn('Failed to send welcome email to user:', user.email);
    }

    // Generate auth token
    const authToken = newUser.getSignedJwtToken();

    return res.status(201).json({
      status: 'success',
      message: 'Fleet User registered successfully',
      data: { ...returnedUser, authToken }
    });
  } catch (error: unknown) {
    // Rollback transaction on error
    await session.abortTransaction();
    const err = error as Error;
    Logger.error('Error creating fleet user:', err);
    return res.status(500).json({
      status: 'failed',
      message: 'Failed to create fleet user',
      error: err.message
    });
  } finally {
    await session.endSession();
  }
});

export const sendOTP = asyncHandler(async (req: Request, res: Response) => {
  const { email, phoneNumber } = req.body as { email: string; phoneNumber: string };

  if (!email && !phoneNumber) {
    return res.status(422).json({
      status: 'failed',
      error: 'Either email or phoneNumber must be provided'
    });
  }

  if (email && phoneNumber) {
    return res.status(422).json({
      status: 'failed',
      error: 'Provide either email or phoneNumber, not both'
    });
  }

  let formattedNumber: string | undefined;
  if (phoneNumber) {
    try {
      const validation = validatePhoneNumber(phoneNumber);
      if (!validation.isValid) {
        return res.status(422).json({
          status: 'failed',
          error: `Invalid phone number ${phoneNumber} provided`
        });
      }
      formattedNumber = validation.formattedNumber;
      req.body.phoneNumber = formattedNumber;
    } catch (error) {
      Logger.error('Error validating phone number:', error);
      return res.status(422).json({
        status: 'failed',
        error: 'Invalid phone number format'
      });
    }
  }

  const { error } = OtpApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({
      status: 'failed',
      error: error.details[0].message
    });
  }

  try {
    // Find user with either the original or formatted number
    const user = await User.findOne({
      $or: [{ email }, { phoneNumber: formattedNumber }, { phoneNumber: phoneNumber }]
    });

    if (!user) {
      return res.status(404).json({
        status: 'failed',
        message: 'No user found with the provided email or phone number'
      });
    }

    const otpExists = user.token?.otp;
    if (otpExists && !hasExpired(user.token.expiredAt)) {
      Logger.info('OTP already exists and is still valid');
      return res.status(200).json({
        status: 'success',
        message: 'Token has already been sent'
      });
    }

    const sendType = email ? OTPSendType.Email : OTPSendType.SMS;
    const { success, otp, message } = await sendOTPToUser(user, sendType);

    if (success) {
      return res.status(200).json({
        status: 'success',
        message: 'An OTP has been sent to your email', //TODO-sms: Remove this once SMS services are fixed
        // message: `An OTP has been sent to your ${sendType === OTPSendType.Email ? 'email' : 'phone number'}`,
        data: otp
      });
    }

    return res.status(500).json({
      status: 'failed',
      message: 'Failed to process OTP request',
      error: message
    });
  } catch (err) {
    Logger.error('Error in sendOTP controller:', err);
    return res.status(500).json({
      status: 'failed',
      message: 'Failed to process OTP request',
      error: err instanceof Error ? err.message : 'Internal server error'
    });
  }
});

export const verifyOTP = asyncHandler(async (req: Request, res: Response) => {
  const { phoneNumber, email, otp } = req.body as { otp: string; phoneNumber: string; email: string };
  const { error } = resetPasswordTokenApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  let formattedNumber: string | undefined;
  if (phoneNumber) {
    const validation = validatePhoneNumber(phoneNumber);
    if (!validation.isValid) {
      return res.status(422).json({ error: `Invalid phone number ${phoneNumber} provided` });
    }
    formattedNumber = validation.formattedNumber;
  }

  const user = await User.findOne({ $or: [{ email }, { phoneNumber: formattedNumber || phoneNumber }] });

  const otpValid = user.token?.otp === otp;
  if (otpValid && !hasExpired(user.token.expiredAt)) {
    const authToken = user.getSignedJwtToken();
    // Remove the token field entirely after successful verification
    await User.findByIdAndUpdate(user._id, { $unset: { token: 1 } });
    return res.status(200).json({
      status: 'success',
      authToken
    });
  } else {
    return res.status(404).json({
      status: 'failed',
      message: 'Invalid Token'
    });
  }
});

export const resetPassword = asyncHandler(async (req: Request, res: Response) => {
  const { newPassword } = req.body as { newPassword: string };
  const { error } = passwordResetApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const reqUser = req.user as UserDoc;
  const user = await User.findOne({ email: reqUser.email });

  user.password = newPassword;
  user.token = null;
  await user.save();

  return res.status(200).json({
    status: 'success',
    message: 'password changed successfully'
  });
});

export const changePassword = asyncHandler(async (req: Request, res: Response) => {
  const { oldPassword, newPassword } = req.body as { oldPassword: string; newPassword: string; email: string };
  const { error } = updatePasswordApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const reqUser = req.user as UserDoc;
  const user = await User.findOne({ email: reqUser.email });

  const isMatch = await user.matchPassword(oldPassword);
  if (!isMatch) {
    return res.status(401).json({
      status: 'failed',
      message: 'Old password Is Wrong'
    });
  }

  user.password = newPassword;
  await user.save();
  const authToken = user.getSignedJwtToken();

  return res.status(200).json({
    status: 'success',
    message: 'password updated successfully',
    authToken
  });
});

export const getAdmins = asyncHandler(async (req: Request, res: Response) => {
  const admins = await User.find({ role: 'admin' });
  return res.status(200).json({
    status: 'success',
    data: admins
  });
});

export const getSuperAdmins = asyncHandler(async (req: Request, res: Response) => {
  const superAdmins = await User.find({ role: 'super-admin' });
  return res.status(200).json({
    status: 'success',
    data: superAdmins
  });
});

export const getManagers = async (req: Request, res: Response) => {
  const managers = await User.find({ role: 'manager' });
  return res.status(200).json({
    status: 'success',
    data: managers
  });
};

export const updateUserRole = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { role } = req.body as { role: USER_ROLE.MANAGER | USER_ROLE.ADMIN };

  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  if (!role || ![USER_ROLE.MANAGER, USER_ROLE.ADMIN].includes(role)) {
    return res.status(422).json({ error: 'role must be either manager or admin' });
  }

  const user = await User.findOneActive({ _id: id });
  if (!user) {
    return res.status(404).json({ error: 'user not found' });
  }

  await User.findOneAndUpdate({ _id: id }, { role });

  return res.status(200).json({
    status: 'success',
    message: 'User role updated successfully'
  });
});
