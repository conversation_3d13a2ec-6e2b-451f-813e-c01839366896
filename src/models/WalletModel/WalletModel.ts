import mongoose, { Model, Schema } from 'mongoose';

import { WalletDoc } from '../../types/wallet.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type WalletDocumentResult = WalletDoc & BaseDocument<WalletDoc>;

type WalletModel = BaseModelMethods<WalletDocumentResult> & Model<WalletDoc>;

const walletSchema = new mongoose.Schema<WalletDocumentResult, WalletModel>(
  {
    // Current available balance (net earnings - withdrawals)
    availableBalance: {
      type: Number,
      default: 0,
      min: 0
    },
    // Total gross earnings before any deductions
    grossEarnings: {
      type: Number,
      default: 0,
      min: 0
    },
    // Total net earnings after platform fees and deductions
    netEarnings: {
      type: Number,
      default: 0,
      min: 0
    },
    // Total amount withdrawn from wallet
    totalWithdrawn: {
      type: Number,
      default: 0,
      min: 0
    },
    // Total platform fees deducted
    totalPlatformFees: {
      type: Number,
      default: 0,
      min: 0
    },
    // Total refunds processed
    totalRefunds: {
      type: Number,
      default: 0,
      min: 0
    },
    // Total pending earnings (not yet available for withdrawal)
    pendingEarnings: {
      type: Number,
      default: 0,
      min: 0
    },
    // Company this wallet belongs to
    company: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: true,
      unique: true // One wallet per company
    },
    // Audit fields
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    // Last reconciliation date for audit purposes
    lastReconciledAt: {
      type: Date
    },
    // Version for optimistic locking
    version: {
      type: Number,
      default: 0
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true,
    // Enable optimistic concurrency control
    optimisticConcurrency: true
  }
);

// Add validation for balance consistency
walletSchema.pre('save', function (next) {
  // Ensure availableBalance = netEarnings - totalWithdrawn
  const expectedBalance = this.netEarnings - this.totalWithdrawn;
  if (Math.abs(this.availableBalance - expectedBalance) > 0.01) {
    // Allow small rounding differences
    this.availableBalance = expectedBalance;
  }

  // Ensure netEarnings = grossEarnings - totalPlatformFees
  const expectedNetEarnings = this.grossEarnings - this.totalPlatformFees;
  if (Math.abs(this.netEarnings - expectedNetEarnings) > 0.01) {
    this.netEarnings = expectedNetEarnings;
    this.availableBalance = this.netEarnings - this.totalWithdrawn;
  }

  next();
});

// Add indexes for performance
walletSchema.index({ company: 1 }, { unique: true });
walletSchema.index({ isActive: 1 });
walletSchema.index({ createdAt: 1 });

walletSchema.static('findOneActive', findOneActive);
walletSchema.static('findActive', findActive);
walletSchema.static('findAndPopulate', findAndPopulate);
walletSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Wallet = mongoose.model<WalletDocumentResult, WalletModel>('Wallet', walletSchema);
export default Wallet;
