import mongoose, { Model, Schema } from 'mongoose';

import { PaymentInfoDoc } from '../../types/payment_info.js';
import { WalletDoc } from '../../types/wallet.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type WalletDocumentResult = WalletDoc & BaseDocument<WalletDoc>;

type PaymentInfoModel = BaseModelMethods<WalletDocumentResult> & Model<PaymentInfoDoc>;

const walletSchema = new mongoose.Schema<WalletDocumentResult, PaymentInfoModel>(
  {
    availableBalance: {
      type: Number,
      default: 0
    },
    totalEarned: {
      type: Number,
      default: 0
    },
    totalWithdrawn: {
      type: Number,
      default: 0
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

walletSchema.static('findOneActive', findOneActive);
walletSchema.static('findActive', findActive);
walletSchema.static('findAndPopulate', findAndPopulate);
walletSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Wallet = mongoose.model<WalletDocumentResult, PaymentInfoModel>('Wallet', walletSchema);
export default Wallet;
