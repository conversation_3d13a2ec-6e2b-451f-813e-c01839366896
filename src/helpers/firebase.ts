/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import firebase from 'firebase-admin';
import { readFileSync } from 'fs';
import path from 'path';

const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH;

if (!serviceAccountPath) {
  throw new Error('SERVICE_ACCOUNT_KEY_PATH is not set in the environment variables.');
}

const serviceAccount = JSON.parse(readFileSync(path.resolve(serviceAccountPath), 'utf-8'));

firebase.initializeApp({
  credential: firebase.credential.cert(serviceAccount)
});

export default firebase;
