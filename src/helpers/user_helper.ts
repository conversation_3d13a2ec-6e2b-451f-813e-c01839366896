/* eslint-disable @typescript-eslint/no-explicit-any */
import Africastalking from 'africastalking';
import libPhone from 'google-libphonenumber';
import jwt from 'jsonwebtoken';
import twilio from 'twilio';

import { emailTemplates } from '../consts.js';
import Logger from '../libs/logger.js';
import Company from '../models/CompanyModel/CompanyModel.js';
import Transaction from '../models/TransactionModel/TransactionModel.js';
import User from '../models/UserModel/UserModel.js';
import { ClientDoc } from '../types/client.js';
import { OTPResponse, OTPSendType } from '../types/otp.js';
import { PaymentInfoDoc } from '../types/payment_info.js';
import { TRANSACTION_TYPE } from '../types/transactions.js';
import { SanitizedUserDoc, UserDoc } from '../types/user.js';
import { sendMail } from './mail_helper.js';

export const sanitizeReturnedUser = (user: UserDoc, paymentInfo: PaymentInfoDoc = undefined) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password: newPassword, ...otherFields } = user;
  const { accountName, bankName, accountNumber, email } = paymentInfo;
  otherFields.paymentInfo = {
    accountName,
    bankName,
    accountNumber,
    email
  };
  return otherFields;
};

export const sanitizeClientUser = (client: ClientDoc): SanitizedUserDoc => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, isActive, token, resetPasswordToken, ...sanitizedClient } = client;
  return sanitizedClient as SanitizedUserDoc;
};

export const passwordGenerator = () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let password = '';
  for (let i = 0; i < 6; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    password += characters[randomIndex];
  }
  return password;
};

export const sendOTPToUser = async (user: UserDoc, sendType: OTPSendType): Promise<OTPResponse> => {
  if (!user) {
    return { success: false, otp: null, message: 'User is required' };
  }

  // Validate required fields based on send type
  if (
    (sendType === OTPSendType.Email && !user.email) ||
    ((sendType === OTPSendType.SMS || sendType === OTPSendType.WhatsApp) && !user.phoneNumber)
  ) {
    return {
      success: false,
      otp: null,
      message: `${sendType} requires ${sendType === OTPSendType.Email ? 'email' : 'phone number'}`
    };
  }

  try {
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const expiredAt = new Date(Date.now() + 5 * 60 * 1000).toISOString();

    // Initialize token object if it doesn't exist
    const updateData = {
      token: {
        otp,
        expiredAt
      }
    };

    // Update user with OTP in a single operation
    const updatedUser = await User.findOneAndUpdate({ _id: user._id }, { $set: updateData }, { new: true }).lean();

    if (!updatedUser) {
      Logger.error('Failed to update user with OTP:', { userId: user._id });
      return { success: false, otp: null, message: 'Failed to update user with OTP' };
    }

    let sent = false;

    // Default to email since SMS services are currently blocked
    // TODO-sms: Remove this override once SMS services are fixed, and uncomment the switch statement below
    sendType = OTPSendType.Email;
    const eventType = 'PASSWORD_RESET';
    const template = emailTemplates[eventType];
    if (!template) {
      Logger.error('Email template not found for event type:', eventType);
      return {
        success: false,
        otp: null,
        message: 'Email template not found'
      };
    }
    try {
      const emailSent = await sendMail({ doc: updatedUser, ...template }, updatedUser);
      if (!emailSent) {
        Logger.error('Failed to send email to:', updatedUser.email);
      } else {
        sent = true;
      }
    } catch (error) {
      Logger.error('Error sending email:', error);
    }

    // switch (sendType) {
    //   case OTPSendType.Email: {
    //     const eventType = 'PASSWORD_RESET';
    //     const template = emailTemplates[eventType];
    //     if (!template) {
    //       Logger.error('Email template not found for event type:', eventType);
    //       return {
    //         success: false,
    //         otp: null,
    //         message: 'Email template not found'
    //       };
    //     }
    //     try {
    //       const emailSent = await sendMail({ doc: updatedUser, ...template }, updatedUser);
    //       if (!emailSent) {
    //         Logger.error('Failed to send email to:', updatedUser.email);
    //         return {
    //           success: false,
    //           otp: null,
    //           message: 'Failed to send email'
    //         };
    //       }
    //       sent = true;
    //     } catch (error) {
    //       Logger.error('Error sending email:', error);
    //       return {
    //         success: false,
    //         otp: null,
    //         message: 'Error sending email'
    //       };
    //     }
    //     break;
    //   }

    //   case OTPSendType.SMS:
    //     try {
    //       const { success, provider } = await sendSMSWithBulkSMSNigeria(user.phoneNumber, otp);
    //       sent = success;
    //       if (!success) {
    //         Logger.error(`Failed to send SMS via ${provider}`);
    //         return {
    //           success: false,
    //           otp: null,
    //           message: `Failed to send SMS via ${provider}`
    //         };
    //       }
    //     } catch (error) {
    //       Logger.error('Error sending SMS:', error);
    //       return {
    //         success: false,
    //         otp: null,
    //         message: 'Error sending SMS'
    //       };
    //     }
    //     break;

    //   case OTPSendType.WhatsApp:
    //     try {
    //       sent = await sendWhatsAppMessage(user.phoneNumber, otp);
    //       if (!sent) {
    //         Logger.error('Failed to send WhatsApp message');
    //       }
    //     } catch (error) {
    //       Logger.error('Error sending WhatsApp message:', error);
    //     }
    //     break;

    //   default:
    //     Logger.error(`Unsupported send type: ${String(sendType)}`);
    // }

    if (!sent) {
      // If sending failed, remove the OTP from the user
      await User.findByIdAndUpdate(user._id, { $unset: { token: 1 } });
      return {
        success: false,
        otp: null,
        message: `Failed to send OTP via ${sendType}`
      };
    }

    return {
      success: true,
      otp: process.env.NODE_ENV === 'development' ? otp : null,
      message: 'OTP sent successfully'
    };
  } catch (error) {
    Logger.error('Error in sendOTPToUser:', error);
    return {
      success: false,
      otp: null,
      message: error instanceof Error ? error.message : 'Error sending OTP'
    };
  }
};

export const sendWelcomeEmail = async (user: UserDoc): Promise<boolean> => {
  try {
    const eventType = 'WELCOME_EMAIL';
    const result = emailTemplates[eventType];
    if (!result) {
      Logger.error('Email template not found for the given event type:', eventType);
      return false;
    }

    const { template, subject } = result;
    const emailData = { doc: user, template, subject };
    const success = await sendMail(emailData, user);
    if (success) {
      Logger.info(`Welcome email sent successfully to ${user.email}`);
      return true;
    }
    return false;
  } catch (error) {
    Logger.error('Error sending welcome email:', error);
    return false;
  }
};

export const hasExpired = (expiredAt: string) => {
  const date = new Date(expiredAt);
  const now = new Date();
  const differenceInMilliseconds = now.getTime() - date.getTime();
  const differenceInMinutes = differenceInMilliseconds / (1000 * 60);
  return differenceInMinutes > 5;
};

export type TokenResult = {
  valid: boolean;
  user?: UserDoc;
};
export const validateAuthToken = async (authToken: string): Promise<TokenResult> => {
  const JWT_SECRET = process.env.JWT_SECRET;
  const decoded = jwt.verify(authToken, JWT_SECRET) as { email: string; password: string } | string;
  if (typeof decoded === 'string') {
    return { valid: false };
  }

  const user = await User.findOne({ email: decoded.email });

  if (!user || user.password !== decoded.password) {
    return { valid: false };
  }
  return { user, valid: true };
};

export const sendSMSWithBulkSMSNigeria = async (
  phoneNumber: string,
  otp: string
): Promise<{ success: boolean; provider: string }> => {
  const apiToken = process.env.BULK_SMS_NIGERIA_API_TOKEN;
  const from = process.env.BULK_SMS_NIGERIA_FROM;

  if (!apiToken || !from) {
    Logger.error('BulkSMSNigeria credentials not found');
    return { success: false, provider: 'BulkSMSNigeria' };
  }

  try {
    // Format the phone number for BulkSMSNigeria
    const { formattedNumber, isValid } = validatePhoneNumber(phoneNumber);
    if (!isValid) {
      Logger.error('Invalid phone number:', { phoneNumber });
      return { success: false, provider: 'BulkSMSNigeria' };
    }

    const response = await fetch('https://www.bulksmsnigeria.com/api/v2/sms', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        body: `Your LuxLet OTP code is ${otp}`,
        from,
        to: formattedNumber,
        // to: formattedNumber.replace('+', ''), // Remove + for BulkSMSNigeria
        api_token: apiToken,
        gateway: 'direct-refund',
        customer_reference: `LuxLet-${Date.now()}`
      })
    });

    const result = await response.json();

    if (result.data?.status === 'success') {
      Logger.info(`SMS message sent successfully via BulkSMSNigeria: ${result.data.message_id}`);
      return { success: true, provider: 'BulkSMSNigeria' };
    }

    const errorMessage = result.error?.message || 'Unknown error from BulkSMSNigeria';
    Logger.error(`BulkSMSNigeria API error - ${errorMessage}`);
    return { success: false, provider: 'BulkSMSNigeria' };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    Logger.error('Error sending SMS message via BulkSMSNigeria:', { error: errorMessage, phoneNumber });
    return { success: false, provider: 'BulkSMSNigeria' };
  }
};

export const sendSMSWithAfricastalking = async (
  phoneNumber: string,
  otp: string
): Promise<{ success: boolean; provider: string }> => {
  const username = process.env.AFRICASTALKING_USERNAME;
  const apiKey = process.env.AFRICASTALKING_API_KEY;
  const from = process.env.AFRICASTALKING_FROM;

  if (!username || !apiKey) {
    Logger.error('Africastalking credentials not found');
    return { success: false, provider: 'Africastalking' };
  }

  const credentials = {
    apiKey,
    username
  };

  try {
    // Format the phone number for Africastalking
    const { formattedNumber, isValid } = validatePhoneNumber(phoneNumber);
    if (!isValid) {
      Logger.error('Invalid phone number format for Africastalking:', { phoneNumber });
      return { success: false, provider: 'Africastalking' };
    }

    const africastalking = Africastalking(credentials);
    const sms = africastalking.SMS;

    const result = await sms.send({
      to: formattedNumber,
      from,
      message: `Your LuxLet OTP code is ${otp}`
    });

    if (result.SMSMessageData?.Recipients?.[0]?.messageId) {
      Logger.info(`SMS message sent successfully via Africastalking: ${result.SMSMessageData.Recipients[0].messageId}`);
      return { success: true, provider: 'Africastalking' };
    }

    const errorMessage = result.SMSMessageData?.Message || 'No message ID returned from Africastalking';
    Logger.error(`Africastalking API error - ${errorMessage}`);
    return { success: false, provider: 'Africastalking' };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    Logger.error('Error sending SMS message via Africastalking:', { error: errorMessage, phoneNumber });
    return { success: false, provider: 'Africastalking' };
  }
};

export const sendSMSWithTwilio = async (
  phoneNumber: string,
  otp: string
): Promise<{ success: boolean; provider: string }> => {
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;
  const from = process.env.TWILIO_FROM;

  if (!accountSid || !authToken || !from) {
    Logger.error('Twilio credentials not found');
    return { success: false, provider: 'Twilio' };
  }

  try {
    // Format the phone number for Twilio
    const { formattedNumber, isValid } = validatePhoneNumber(phoneNumber);
    if (!isValid) {
      Logger.error('Invalid phone number format for Twilio:', { phoneNumber });
      return { success: false, provider: 'Twilio' };
    }

    const twilioClient = twilio(accountSid, authToken);

    const message = await twilioClient.messages.create({
      body: `Your LuxLet OTP code is ${otp}`,
      from,
      to: formattedNumber
    });

    if (message.sid) {
      Logger.info(`SMS message sent successfully via Twilio: ${message.sid}`);
      return { success: true, provider: 'Twilio' };
    }

    Logger.error('No message SID returned from Twilio', { phoneNumber });
    return { success: false, provider: 'Twilio' };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    Logger.error('Error sending SMS message via Twilio:', { error: errorMessage, phoneNumber });
    return { success: false, provider: 'Twilio' };
  }
};

export const sendWhatsAppMessage = async (phoneNumber: string, otp: string) => {
  const sendTo = `whatsapp:${phoneNumber}`;
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;
  const from = process.env.TWILIO_FROM;
  const whatsappClient = twilio(accountSid, authToken);
  try {
    const message = await whatsappClient.messages.create({
      from,
      to: sendTo,
      body: `Your LuxLet OTP code is ${otp}`
    });
    if (message.sid) {
      Logger.info(`WhatsApp message sent successfully: ${message.sid}`);
      return true;
    }
  } catch (error) {
    Logger.error('Error sending WhatsApp message:', error);
  }
  return false;
};

export const validatePhoneNumber = (phoneNumber: string) => {
  try {
    const phoneUtil = libPhone.PhoneNumberUtil.getInstance();
    const parsedNumber = phoneUtil.parseAndKeepRawInput(phoneNumber, 'NG');
    const isValid = phoneUtil.isValidNumber(parsedNumber);
    const formattedNumber = phoneUtil.format(parsedNumber, libPhone.PhoneNumberFormat.E164);

    return { isValid, formattedNumber };
  } catch (error) {
    throw new Error('Invalid phone number');
  }
};

export const getWalletBalance = async (user: UserDoc) => {
  const company = await Company.findOne({ _id: user.company });
  if (!company) {
    return 'Company not found';
  }

  try {
    // Import WalletService dynamically to avoid circular dependencies
    const { WalletService } = await import('../services/wallet_service.js');

    // Ensure wallet exists for the company
    await WalletService.ensureWalletExists(company._id, user._id);

    // Get comprehensive wallet balance
    const walletBalance = await WalletService.getWalletBalance(company._id);
    return walletBalance;
  } catch (error) {
    // Fallback to legacy calculation if wallet service fails
    type AggregationResult = { totalEarned?: number; totalWithdrawn?: number };
    const [earnings, withdrawals] = await Promise.all([
      Transaction.aggregate<AggregationResult>([
        { $match: { companyId: company._id, type: TRANSACTION_TYPE.CREDIT_WALLET_TRANSACTION } },
        { $group: { _id: null, totalEarned: { $sum: '$amount' } } }
      ]),

      Transaction.aggregate<AggregationResult>([
        { $match: { companyId: company._id, type: TRANSACTION_TYPE.WALLET_WITHDRAWAL_TRANSACTION } },
        { $group: { _id: null, totalWithdrawn: { $sum: '$amount' } } }
      ])
    ]);

    const totalEarned = earnings[0]?.totalEarned ?? 0;
    const totalWithdrawn = withdrawals[0]?.totalWithdrawn ?? 0;
    const availableBalance = totalEarned - totalWithdrawn;

    return {
      availableBalance,
      grossEarnings: totalEarned,
      netEarnings: totalEarned,
      totalWithdrawn,
      totalPlatformFees: 0,
      totalRefunds: 0,
      pendingEarnings: 0
    };
  }
};

export const formatNigerianPhoneNumber = (phoneNumber: string) => {
  return phoneNumber.startsWith('+234')
    ? phoneNumber
    : phoneNumber.startsWith('234')
      ? `+${phoneNumber}`
      : phoneNumber.startsWith('0')
        ? `+234${phoneNumber.substring(1)}`
        : `+234${phoneNumber}`;
};
