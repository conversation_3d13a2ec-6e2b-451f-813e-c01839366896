import mongoose from 'mongoose';

import Logger from '../libs/logger.js';
import Transaction from '../models/TransactionModel/TransactionModel.js';
import Wallet from '../models/WalletModel/WalletModel.js';
import { TRANSACTION_STATUS, TRANSACTION_TYPE } from '../types/transactions.js';
import { CreateWalletRequestBody, WalletBalanceResponse, WalletDoc, WalletTransactionData } from '../types/wallet.js';

/**
 * Comprehensive Wallet Service
 * Handles all wallet operations with proper transaction management and audit trails
 */
export class WalletService {
  /**
   * Create a new wallet for a company
   */
  static async createWallet(data: CreateWalletRequestBody): Promise<WalletDoc> {
    try {
      const existingWallet = await Wallet.findOne({ company: data.company, isActive: true });
      if (existingWallet) {
        throw new Error('Wallet already exists for this company');
      }

      const wallet = new Wallet({
        company: data.company,
        createdBy: data.createdBy,
        updatedBy: data.createdBy,
        availableBalance: 0,
        grossEarnings: 0,
        netEarnings: 0,
        totalWithdrawn: 0,
        totalPlatformFees: 0,
        totalRefunds: 0,
        pendingEarnings: 0,
        version: 0
      });

      await wallet.save();
      Logger.info(`Wallet created for company: ${data.company}`);
      return wallet.toObject();
    } catch (error) {
      Logger.error('Error creating wallet:', error);
      throw error;
    }
  }

  /**
   * Get wallet by company ID
   */
  static async getWalletByCompany(companyId: mongoose.Types.ObjectId): Promise<WalletDoc | null> {
    try {
      const wallet = await Wallet.findOneActive({ company: companyId });
      return wallet?.toObject() || null;
    } catch (error) {
      Logger.error('Error fetching wallet:', error);
      throw error;
    }
  }

  /**
   * Get wallet balance information
   */
  static async getWalletBalance(companyId: mongoose.Types.ObjectId): Promise<WalletBalanceResponse> {
    try {
      const wallet = await this.getWalletByCompany(companyId);
      if (!wallet) {
        throw new Error('Wallet not found for company');
      }

      return {
        availableBalance: wallet.availableBalance,
        grossEarnings: wallet.grossEarnings,
        netEarnings: wallet.netEarnings,
        totalWithdrawn: wallet.totalWithdrawn,
        totalPlatformFees: wallet.totalPlatformFees,
        totalRefunds: wallet.totalRefunds,
        pendingEarnings: wallet.pendingEarnings,
        lastReconciledAt: wallet.lastReconciledAt
      };
    } catch (error) {
      Logger.error('Error getting wallet balance:', error);
      throw error;
    }
  }

  /**
   * Add earnings to wallet (from booking approval)
   */
  static async addEarnings(
    companyId: mongoose.Types.ObjectId,
    grossAmount: number,
    netAmount: number,
    platformFee: number,
    transactionData: WalletTransactionData,
    updatedBy: mongoose.Types.ObjectId
  ): Promise<{ wallet: WalletDoc; transaction: any }> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get wallet with session for atomic update
      const wallet = await Wallet.findOne({ company: companyId, isActive: true }).session(session);
      if (!wallet) {
        throw new Error('Wallet not found for company');
      }

      // Update wallet balances
      wallet.grossEarnings += grossAmount;
      wallet.totalPlatformFees += platformFee;
      wallet.netEarnings += netAmount;
      wallet.availableBalance += netAmount;
      wallet.updatedBy = updatedBy;
      wallet.version += 1;

      await wallet.save({ session });

      // Create transaction record for earnings
      const earningsTransaction = new Transaction({
        companyId,
        description: transactionData.description,
        bookingId: transactionData.bookingId,
        reference: transactionData.reference,
        currency: 'NGN',
        amount: netAmount,
        type: TRANSACTION_TYPE.BOOKING_EARNINGS,
        status: TRANSACTION_STATUS.SUCCESS,
        meta: {
          grossAmount,
          platformFee,
          ...transactionData.metadata
        }
      });

      await earningsTransaction.save({ session });

      // Create separate transaction for platform fee if applicable
      if (platformFee > 0) {
        const feeTransaction = new Transaction({
          companyId,
          description: `Platform fee for ${transactionData.description}`,
          bookingId: transactionData.bookingId,
          reference: `FEE_${transactionData.reference}`,
          currency: 'NGN',
          amount: platformFee,
          type: TRANSACTION_TYPE.PLATFORM_FEE,
          status: TRANSACTION_STATUS.SUCCESS,
          meta: {
            relatedEarningsReference: transactionData.reference,
            ...transactionData.metadata
          }
        });

        await feeTransaction.save({ session });
      }

      await session.commitTransaction();
      Logger.info(`Earnings added to wallet: ${companyId}, Net: ${netAmount}, Fee: ${platformFee}`);

      return {
        wallet: wallet.toObject(),
        transaction: earningsTransaction.toObject()
      };
    } catch (error) {
      await session.abortTransaction();
      Logger.error('Error adding earnings to wallet:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Process withdrawal from wallet
   */
  static async processWithdrawal(
    companyId: mongoose.Types.ObjectId,
    amount: number,
    transactionData: WalletTransactionData,
    updatedBy: mongoose.Types.ObjectId
  ): Promise<{ wallet: WalletDoc; transaction: any }> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const wallet = await Wallet.findOne({ company: companyId, isActive: true }).session(session);
      if (!wallet) {
        throw new Error('Wallet not found for company');
      }

      if (wallet.availableBalance < amount) {
        throw new Error('Insufficient balance for withdrawal');
      }

      // Update wallet balances
      wallet.availableBalance -= amount;
      wallet.totalWithdrawn += amount;
      wallet.updatedBy = updatedBy;
      wallet.version += 1;

      await wallet.save({ session });

      // Create transaction record
      const withdrawalTransaction = new Transaction({
        companyId,
        description: transactionData.description,
        reference: transactionData.reference,
        currency: 'NGN',
        amount,
        type: TRANSACTION_TYPE.WALLET_WITHDRAWAL,
        status: TRANSACTION_STATUS.SUCCESS,
        meta: transactionData.metadata
      });

      await withdrawalTransaction.save({ session });

      await session.commitTransaction();
      Logger.info(`Withdrawal processed for wallet: ${companyId}, Amount: ${amount}`);

      return {
        wallet: wallet.toObject(),
        transaction: withdrawalTransaction.toObject()
      };
    } catch (error) {
      await session.abortTransaction();
      Logger.error('Error processing withdrawal:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Process refund (deduct from wallet)
   */
  static async processRefund(
    companyId: mongoose.Types.ObjectId,
    amount: number,
    transactionData: WalletTransactionData,
    updatedBy: mongoose.Types.ObjectId
  ): Promise<{ wallet: WalletDoc; transaction: any }> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const wallet = await Wallet.findOne({ company: companyId, isActive: true }).session(session);
      if (!wallet) {
        throw new Error('Wallet not found for company');
      }

      // Update wallet balances
      wallet.totalRefunds += amount;
      wallet.netEarnings -= amount;
      wallet.availableBalance -= amount;
      wallet.updatedBy = updatedBy;
      wallet.version += 1;

      await wallet.save({ session });

      // Create transaction record
      const refundTransaction = new Transaction({
        companyId,
        description: transactionData.description,
        reference: transactionData.reference,
        currency: 'NGN',
        amount,
        type: TRANSACTION_TYPE.REFUND_PAYMENT,
        status: TRANSACTION_STATUS.SUCCESS,
        meta: transactionData.metadata
      });

      await refundTransaction.save({ session });

      await session.commitTransaction();
      Logger.info(`Refund processed for wallet: ${companyId}, Amount: ${amount}`);

      return {
        wallet: wallet.toObject(),
        transaction: refundTransaction.toObject()
      };
    } catch (error) {
      await session.abortTransaction();
      Logger.error('Error processing refund:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Reconcile wallet balances with transaction history
   */
  static async reconcileWallet(companyId: mongoose.Types.ObjectId): Promise<WalletDoc> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const wallet = await Wallet.findOne({ company: companyId, isActive: true }).session(session);
      if (!wallet) {
        throw new Error('Wallet not found for company');
      }

      // Calculate balances from transaction history
      const aggregationResults = await Transaction.aggregate([
        { $match: { companyId, isActive: true } },
        {
          $group: {
            _id: '$type',
            totalAmount: { $sum: '$amount' }
          }
        }
      ]).session(session);

      // Reset wallet balances
      let grossEarnings = 0;
      let totalPlatformFees = 0;
      let totalWithdrawn = 0;
      let totalRefunds = 0;

      aggregationResults.forEach(result => {
        switch (result._id) {
          case TRANSACTION_TYPE.BOOKING_EARNINGS:
          case TRANSACTION_TYPE.CREDIT_WALLET_TRANSACTION:
            grossEarnings += result.totalAmount;
            break;
          case TRANSACTION_TYPE.PLATFORM_FEE:
            totalPlatformFees += result.totalAmount;
            break;
          case TRANSACTION_TYPE.WALLET_WITHDRAWAL:
          case TRANSACTION_TYPE.WALLET_WITHDRAWAL_TRANSACTION:
            totalWithdrawn += result.totalAmount;
            break;
          case TRANSACTION_TYPE.REFUND_PAYMENT:
            totalRefunds += result.totalAmount;
            break;
        }
      });

      // Update wallet with reconciled values
      wallet.grossEarnings = grossEarnings;
      wallet.totalPlatformFees = totalPlatformFees;
      wallet.netEarnings = grossEarnings - totalPlatformFees;
      wallet.totalWithdrawn = totalWithdrawn;
      wallet.totalRefunds = totalRefunds;
      wallet.availableBalance = wallet.netEarnings - totalWithdrawn;
      wallet.lastReconciledAt = new Date();
      wallet.version += 1;

      await wallet.save({ session });
      await session.commitTransaction();

      Logger.info(`Wallet reconciled for company: ${companyId}`);
      return wallet.toObject();
    } catch (error) {
      await session.abortTransaction();
      Logger.error('Error reconciling wallet:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Ensure wallet exists for a company, create if not exists
   */
  static async ensureWalletExists(
    companyId: mongoose.Types.ObjectId,
    createdBy: mongoose.Types.ObjectId
  ): Promise<WalletDoc> {
    try {
      let wallet = await this.getWalletByCompany(companyId);

      if (!wallet) {
        wallet = await this.createWallet({
          company: companyId,
          createdBy
        });
      }

      return wallet;
    } catch (error) {
      Logger.error('Error ensuring wallet exists:', error);
      throw error;
    }
  }

  /**
   * Get wallet transaction history
   */
  static async getWalletTransactions(
    companyId: mongoose.Types.ObjectId,
    limit: number = 50,
    skip: number = 0
  ): Promise<any[]> {
    try {
      const transactions = await Transaction.find({
        companyId,
        isActive: true,
        type: {
          $in: [
            TRANSACTION_TYPE.BOOKING_EARNINGS,
            TRANSACTION_TYPE.PLATFORM_FEE,
            TRANSACTION_TYPE.WALLET_WITHDRAWAL,
            TRANSACTION_TYPE.REFUND_PAYMENT,
            TRANSACTION_TYPE.CREDIT_WALLET_TRANSACTION,
            TRANSACTION_TYPE.WALLET_WITHDRAWAL_TRANSACTION
          ]
        }
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .lean();

      return transactions;
    } catch (error) {
      Logger.error('Error fetching wallet transactions:', error);
      throw error;
    }
  }

  /**
   * Calculate platform fee based on gross amount
   */
  static calculatePlatformFee(grossAmount: number, feePercentage: number = 0.2): number {
    return Number((grossAmount * feePercentage).toFixed(2));
  }

  /**
   * Validate wallet balance consistency
   */
  static async validateWalletConsistency(companyId: mongoose.Types.ObjectId): Promise<{
    isConsistent: boolean;
    discrepancies: string[];
    wallet: WalletDoc;
  }> {
    try {
      const wallet = await this.getWalletByCompany(companyId);
      if (!wallet) {
        throw new Error('Wallet not found for company');
      }

      const discrepancies: string[] = [];

      // Check if availableBalance = netEarnings - totalWithdrawn
      const expectedAvailableBalance = wallet.netEarnings - wallet.totalWithdrawn;
      if (Math.abs(wallet.availableBalance - expectedAvailableBalance) > 0.01) {
        discrepancies.push(
          `Available balance mismatch: Expected ${expectedAvailableBalance}, Got ${wallet.availableBalance}`
        );
      }

      // Check if netEarnings = grossEarnings - totalPlatformFees
      const expectedNetEarnings = wallet.grossEarnings - wallet.totalPlatformFees;
      if (Math.abs(wallet.netEarnings - expectedNetEarnings) > 0.01) {
        discrepancies.push(`Net earnings mismatch: Expected ${expectedNetEarnings}, Got ${wallet.netEarnings}`);
      }

      return {
        isConsistent: discrepancies.length === 0,
        discrepancies,
        wallet
      };
    } catch (error) {
      Logger.error('Error validating wallet consistency:', error);
      throw error;
    }
  }
}
