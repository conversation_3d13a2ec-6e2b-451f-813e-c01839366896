import express from 'express';

import {
  approveBooking,
  cancelBooking,
  createBooking,
  getAllBookings,
  getClientBooking,
  getManagerBooking,
  getOneBooking,
  getRejectedBookings,
  previewBooking
} from '../controllers/bookings_controller.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// Create booking
router.post('/', authorizeClient, createBooking);

// Preview booking (before creating)
router.post('/preview', authorizeClient, previewBooking);

// Approve booking (specific action)
router.post('/approve/:id', authorize, restrictToRoles(['admin', 'manager']), approveBooking);

// Get rejected bookings (for super admin)
router.get('/rejected', authorize, restrictToRoles(['super-admin']), getRejectedBookings);

// Cancel booking
router.patch('/:id', authorizeClient, cancelBooking);

// Fetch bookings by role (static, must come before dynamic ':id')
router.get('/manager/', authorize, restrictToRoles(['admin', 'manager']), getManagerBooking);
router.get('/client/', authorizeClient, getClientBooking);

// Fetch single booking by ID (dynamic, comes after static GETs)
router.get(
  '/:id',
  async (req, res, next) => {
    try {
      // Try client auth first
      try {
        await authorizeClient(req, res, () => {}, true);
        return next();
      } catch (_) {
        // If client auth fails, try user auth
        try {
          await authorize(req, res, () => {}, true);
          return next();
        } catch (_) {
          return res.status(403).json({
            status: 'failed',
            message: 'Unauthorized access'
          });
        }
      }
    } catch (error) {
      next(error);
    }
  },
  restrictToRoles(['admin', 'manager', 'client']),
  getOneBooking
);

// Fetch all bookings
router.get(
  '/',
  async (req, res, next) => {
    try {
      // Try client auth first
      try {
        await authorizeClient(req, res, () => {}, true);
        return next();
      } catch (_) {
        // If client auth fails, try user auth
        try {
          await authorize(req, res, () => {}, true);
          return next();
        } catch (_) {
          return res.status(403).json({
            status: 'failed',
            message: 'Unauthorized access'
          });
        }
      }
    } catch (error) {
      next(error);
    }
  },
  restrictToRoles(['admin', 'manager', 'client']),
  getAllBookings
);

export default router;
