import express from 'express';

import { createBrand, deleteBrand, getBrand, updateBrand } from '../controllers/brand_controller.js';
import { authorize, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// Create a brand
router.post('/', authorize, restrictToRoles(['admin']), createBrand);

// Update a specific brand
router.put('/:id', authorize, restrictToRoles(['admin']), updateBrand);

// Delete a specific brand
router.delete('/:id', authorize, restrictToRoles(['admin']), deleteBrand);

// Get a specific brand
router.get('/:id', authorize, restrictToRoles(['admin']), getBrand);

export default router;
