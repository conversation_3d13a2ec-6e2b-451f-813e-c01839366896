import express from 'express';

import {
  getWalletBalance,
  getWalletTransactions,
  reconcileWallet,
  validateWalletConsistency
} from '../controllers/wallet_controller.js';
import { authorize, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// Get wallet balance
router.get('/', authorize, restrictToRoles(['admin', 'manager']), getWalletBalance);

// Get wallet transaction history
router.get('/transactions', authorize, restrictToRoles(['admin', 'manager']), getWalletTransactions);

// Reconcile wallet balances
router.post('/reconcile', authorize, restrictToRoles(['admin', 'manager']), reconcileWallet);

// Validate wallet consistency
router.get('/validate', authorize, restrictToRoles(['admin', 'manager']), validateWalletConsistency);

export default router;
