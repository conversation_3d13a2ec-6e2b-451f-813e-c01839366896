import express from 'express';

import { clientAuthentication, loginWithToken } from '../controllers/clients_controller.js';
import {
  changePassword,
  createFleetUser,
  fleetAppLogin,
  logOut,
  resetPassword,
  sendOTP,
  verifyOTP
} from '../controllers/users_controller.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';
import { USER_ROLE } from '../types/user.js';

const router = express.Router();

// Client-related routes first (more specific)
router.post('/client/authenticate', clientAuthentication);
router.post('/client/login-with-token', authorizeClient, loginWithToken);

// Fleet user management
router.post('/fleet/create', createFleetUser);

// Authentication flows
router.post('/login', fleetAppLogin);
router.get('/logout', logOut);

// OTP handling
router.post('/send-otp', sendOTP);
router.post('/verify-otp', verifyOTP);

// Password management (protected routes)
router.post('/reset-password', authorize, resetPassword);
router.post('/change-password', authorize, restrictToRoles(Object.values(USER_ROLE)), changePassword);

export default router;
