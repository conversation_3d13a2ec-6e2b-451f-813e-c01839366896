import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export enum BOOKING_STATUS {
  PENDING = 'pending',
  DECLINE = 'decline',
  BOOKED = 'booked',
  APPROVED = 'approved',
  CANCELED = 'canceled',
  IN_PROGRESS = 'inProgress',
  COMPLETED = 'completed'
}

export enum TRANSACTION_CHANEL {
  CARD = 'card',
  BANK = 'bank',
  USSD = 'ussd',
  BANK_TRANSFER = 'bank_transfer',
  MOBILE_MONEY = 'mobile_money'
}
export enum TRANSACTION_GATEWAY {
  PAYSTACK = 'paystack',
  FLUTTERWAVE = 'flutterwave'
}

export enum TRANSACTION_STATUS {
  ATTEMPTED = 'attempted',
  SUCCESS = 'success',
  PENDING = 'pending',
  REVERSED = 'reversed',
  FAILED = 'failed'
}

export enum TRANSACTION_TYPE {
  // Client payment transactions
  BOOKING = 'booking',
  CHARGE_MANDATE = 'charge_mandate',

  // Wallet credit transactions (money coming in)
  BOOKING_EARNINGS = 'booking_earnings',
  REFUND_REVERSAL = 'refund_reversal',
  ADJUSTMENT_CREDIT = 'adjustment_credit',

  // Wallet debit transactions (money going out)
  WALLET_WITHDRAWAL = 'wallet_withdrawal',
  PLATFORM_FEE = 'platform_fee',
  REFUND_PAYMENT = 'refund_payment',
  ADJUSTMENT_DEBIT = 'adjustment_debit',

  // Legacy types (for backward compatibility)
  CREDIT_WALLET_TRANSACTION = 'credit_wallet_transaction',
  DEBIT_WALLET_TRANSACTION = 'debit_wallet_transaction',
  WALLET_WITHDRAWAL_TRANSACTION = 'wallet_withdrawal_transaction'
}

export type TransactionDoc = {
  _id: Types.ObjectId;
  id: string;
  clientId: Types.ObjectId;
  companyId: Types.ObjectId;
  bookingId: Types.ObjectId;
  country: {
    name: string;
    code: string;
    currency: string;
  };
  channel: TRANSACTION_CHANEL;
  description: string;
  auth: string;
  email: string;
  message: string;
  amount: number;
  checkoutUrl: string;
  charge: number;
  reference: string;
  merchantReference: string;
  meta: object;
  extra: object;
  gateway: TRANSACTION_GATEWAY;
  status: TRANSACTION_STATUS;
  type: TRANSACTION_TYPE;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  currency?: string;
  callbackUrl?: string;
};

export type AdvancedBookingQueryResult = AdvancedQueryResult<TransactionDoc>;
export type TransactionDocExt = Omit<TransactionDoc, 'createdBy' | 'updatedBy'> & {
  createdBy: { fullName: string };
  updatedBy?: { fullName: string };
};

export type RegisterTransactionRequestBody = Omit<TransactionDoc, '_id' | 'createdAt' | 'updatedAt'>;
