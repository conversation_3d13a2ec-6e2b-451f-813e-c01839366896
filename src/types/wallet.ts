import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type WalletDoc = {
  _id: Types.ObjectId;
  id: string;
  availableBalance: number;
  totalEarned: number;
  totalWithdrawn: number;
  company: Types.ObjectId;
  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
};

export type AdvancedPaymentQueryResult = AdvancedQueryResult<WalletDoc>;
export type RegisterPaymentInfoRequestBody = Omit<
  WalletDoc,
  '_id' | 'id' | 'createdAt' | 'updatedAt' | 'isActive' | 'company' | 'createdBy' | 'updatedBy'
>;
