import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type WalletDoc = {
  _id: Types.ObjectId;
  id: string;
  // Current available balance (net earnings - withdrawals)
  availableBalance: number;
  // Total gross earnings before any deductions
  grossEarnings: number;
  // Total net earnings after platform fees and deductions
  netEarnings: number;
  // Total amount withdrawn from wallet
  totalWithdrawn: number;
  // Total platform fees deducted
  totalPlatformFees: number;
  // Total refunds processed
  totalRefunds: number;
  // Total pending earnings (not yet available for withdrawal)
  pendingEarnings: number;
  // Company this wallet belongs to
  company: Types.ObjectId;
  // Audit fields
  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  // Last reconciliation date for audit purposes
  lastReconciledAt?: Date;
  // Version for optimistic locking
  version: number;
};

export type AdvancedWalletQueryResult = AdvancedQueryResult<WalletDoc>;

export type CreateWalletRequestBody = {
  company: Types.ObjectId;
  createdBy: Types.ObjectId;
};

export type WalletTransactionData = {
  amount: number;
  description: string;
  bookingId?: Types.ObjectId;
  reference: string;
  metadata?: Record<string, any>;
};

export type WalletBalanceResponse = {
  availableBalance: number;
  grossEarnings: number;
  netEarnings: number;
  totalWithdrawn: number;
  totalPlatformFees: number;
  totalRefunds: number;
  pendingEarnings: number;
  lastReconciledAt?: Date;
};
