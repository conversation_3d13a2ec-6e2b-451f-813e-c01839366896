import mongoose from 'mongoose';

async function testConnection() {
  try {
    console.log('Testing MongoDB connection...');
    
    // Try different connection strings
    const connectionStrings = [
      'mongodb://localhost:27017/luxlet',
      'mongodb://127.0.0.1:27017/luxlet',
      'mongodb://0.0.0.0:27017/luxlet'
    ];
    
    for (const uri of connectionStrings) {
      try {
        console.log(`Trying connection: ${uri}`);
        await mongoose.connect(uri, { 
          serverSelectionTimeoutMS: 5000,
          connectTimeoutMS: 5000 
        });
        console.log(`✅ Successfully connected to: ${uri}`);
        
        // Test basic operations
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log(`Found ${collections.length} collections`);
        
        await mongoose.disconnect();
        return uri;
      } catch (error) {
        console.log(`❌ Failed to connect to: ${uri} - ${error.message}`);
        if (mongoose.connection.readyState !== 0) {
          await mongoose.disconnect();
        }
      }
    }
    
    throw new Error('Could not connect to any MongoDB instance');
  } catch (error) {
    console.error('Database connection test failed:', error.message);
    process.exit(1);
  }
}

testConnection();
