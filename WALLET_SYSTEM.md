# Enhanced Wallet System Documentation

## Overview

The enhanced wallet system provides comprehensive financial tracking and management for fleet managers in the LuxLet platform. It includes proper separation of gross/net earnings, platform fees, withdrawals, and maintains audit trails for all financial operations.

## Key Features

### 1. Comprehensive Financial Tracking
- **Gross Earnings**: Total earnings before any deductions
- **Net Earnings**: Earnings after platform fees and deductions
- **Platform Fees**: Fees charged by the platform (default 20%)
- **Available Balance**: Current withdrawable amount
- **Total Withdrawn**: Historical withdrawal amounts
- **Total Refunds**: Refunds processed
- **Pending Earnings**: Earnings not yet available for withdrawal

### 2. Professional Financial Management
- Atomic database transactions for data consistency
- Optimistic concurrency control to prevent race conditions
- Automatic balance validation and reconciliation
- Comprehensive audit trails
- Error handling and rollback mechanisms

### 3. Enhanced Transaction Types
- `BOOKING_EARNINGS`: Net earnings from approved bookings
- `PLATFORM_FEE`: Platform fees deducted from gross earnings
- `WALLET_WITHDRAWAL`: Money withdrawn from wallet
- `REFUND_PAYMENT`: Refunds processed to customers
- `ADJUSTMENT_CREDIT/DEBIT`: Manual adjustments

## Architecture

### Models

#### WalletDoc
```typescript
{
  availableBalance: number;      // Current withdrawable balance
  grossEarnings: number;         // Total gross earnings
  netEarnings: number;          // Earnings after platform fees
  totalWithdrawn: number;       // Total amount withdrawn
  totalPlatformFees: number;    // Total platform fees paid
  totalRefunds: number;         // Total refunds processed
  pendingEarnings: number;      // Earnings pending availability
  company: ObjectId;            // Company reference
  lastReconciledAt: Date;       // Last reconciliation timestamp
  version: number;              // Optimistic locking version
}
```

### Services

#### WalletService
The main service class handling all wallet operations:

- `createWallet()`: Create new wallet for company
- `getWalletBalance()`: Get comprehensive balance information
- `addEarnings()`: Add earnings from booking approval
- `processWithdrawal()`: Process withdrawal requests
- `processRefund()`: Handle refund operations
- `reconcileWallet()`: Reconcile balances with transaction history
- `validateWalletConsistency()`: Validate wallet data integrity

## API Endpoints

### GET /api/wallets
Get wallet balance and summary information.

**Response:**
```json
{
  "status": "success",
  "data": {
    "availableBalance": 15000.00,
    "grossEarnings": 25000.00,
    "netEarnings": 20000.00,
    "totalWithdrawn": 5000.00,
    "totalPlatformFees": 5000.00,
    "totalRefunds": 0.00,
    "pendingEarnings": 0.00,
    "lastReconciledAt": "2024-01-15T10:30:00Z"
  }
}
```

### GET /api/wallets/transactions
Get wallet transaction history with pagination.

**Query Parameters:**
- `limit`: Number of transactions to return (default: 50)
- `skip`: Number of transactions to skip (default: 0)

### POST /api/wallets/reconcile
Manually trigger wallet reconciliation (admin/manager only).

### GET /api/wallets/validate
Validate wallet consistency and return any discrepancies.

## Financial Flow

### Booking Approval Process
1. Manager approves booking
2. System calculates:
   - Gross amount = `booking.managerTotal`
   - Platform fee = `grossAmount * 0.20` (20%)
   - Net amount = `grossAmount - platformFee`
3. Wallet is updated:
   - `grossEarnings += grossAmount`
   - `totalPlatformFees += platformFee`
   - `netEarnings += netAmount`
   - `availableBalance += netAmount`
4. Transactions are created:
   - Earnings transaction (net amount)
   - Platform fee transaction (if applicable)
5. Car statistics updated:
   - `totalEarned += grossAmount`
   - `bookingCount += 1`

### Balance Calculations
- `netEarnings = grossEarnings - totalPlatformFees`
- `availableBalance = netEarnings - totalWithdrawn`

## Migration and Setup

### Running Migration
```bash
# Run the migration script to update existing data
npm run migrate:wallet-system
```

The migration script will:
1. Create wallets for companies without them
2. Reconcile existing wallet balances
3. Validate data consistency
4. Generate a summary report

### Database Indexes
The system creates the following indexes for performance:
- `{ company: 1 }` (unique)
- `{ isActive: 1 }`
- `{ createdAt: 1 }`

## Testing

### Running Tests
```bash
# Run wallet service unit tests
npm test -- wallet_service.spec.ts
```

### Test Coverage
The test suite covers:
- Wallet creation and management
- Earnings processing
- Withdrawal handling
- Balance reconciliation
- Data validation
- Error scenarios

## Error Handling

### Common Errors
- `Wallet not found for company`: Company doesn't have a wallet
- `Insufficient balance for withdrawal`: Withdrawal amount exceeds available balance
- `Wallet already exists for this company`: Attempting to create duplicate wallet

### Recovery Procedures
1. **Data Inconsistency**: Run wallet reconciliation
2. **Missing Wallets**: Run migration script
3. **Balance Discrepancies**: Use validation endpoint to identify issues

## Security Considerations

### Access Control
- Only managers and admins can access wallet endpoints
- Company isolation ensures users only see their own wallet data
- Sensitive operations require proper authentication

### Data Integrity
- Database transactions ensure atomicity
- Optimistic locking prevents concurrent modification issues
- Automatic validation prevents invalid states
- Comprehensive audit trails for all operations

## Monitoring and Maintenance

### Regular Tasks
1. **Daily**: Monitor wallet consistency validation
2. **Weekly**: Review platform fee calculations
3. **Monthly**: Reconcile all wallets and generate reports

### Key Metrics
- Total platform revenue (sum of all platform fees)
- Average earnings per booking
- Withdrawal patterns and frequency
- Wallet balance distribution

## Future Enhancements

### Planned Features
1. **Automated Withdrawals**: Scheduled withdrawal processing
2. **Fee Structures**: Configurable platform fee percentages
3. **Reporting Dashboard**: Advanced analytics and insights
4. **Multi-Currency Support**: Support for different currencies
5. **Escrow System**: Hold earnings for dispute resolution

### Integration Points
- Payment gateway integration for withdrawals
- Accounting system synchronization
- Tax reporting and compliance
- Real-time notifications for financial events
