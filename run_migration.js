import { initializeCarManagerTotalEarned, migrateWalletSystem } from './dist/src/scripts/migrate_wallet_system.js';

async function runMigration() {
  try {
    console.log('Starting wallet system migration...');

    // Load environment variables
    const dotenv = await import('dotenv');
    dotenv.config({ path: '.env.local' });

    // Set MongoDB URI if not set
    if (!process.env.MONGODB_URI) {
      process.env.MONGODB_URI = process.env.MONGO_URL || 'mongodb://localhost:27017/luxlet';
    }

    await migrateWalletSystem();
    await initializeCarManagerTotalEarned();

    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
